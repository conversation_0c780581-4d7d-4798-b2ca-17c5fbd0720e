# -*- coding: utf-8 -*-
"""
@File  : web.py
@Author: <PERSON>, <PERSON><PERSON>
@Date  : 2025/3/3 16:27
@Desc  : 
"""
import os
import sys
import json
import uvicorn
import shutil
import time
from fastapi import FastAPI, Query, Response, HTTPException, Body, Form, UploadFile, File, Path
from fastapi.responses import JSONResponse
from starlette.middleware.cors import CORSMiddleware
from fastapi.requests import Request
import traceback
import pandas as pd
from typing import List
from datetime import datetime
import threading
from pydantic import BaseModel, constr, field_validator

sys.path.append('../')

from lib import logger, proj_dir, _log_conf, Global, MyEncoder
from lib.city_manager import city_manager
from serve.utils import (
    check_function,
    create_lock_file,
    TaskManager,
    calc_web_data_md5,
    calc_str_md5,
    run,
    run_warning,
    run_year_emission,
    run_hosipital_bed,
    thread_run_extract_graph,
    remove_files,
    run_oil_volume,
    run_oil_quantity,
    run_port_base_info,
    run_gas_port,
    run_year_emission_vs_hp,
    run_exe_with_self_monitor,
    run_exec_online_monitor_mismatch,
    run_exe_online_exceeding,
    run_permit_eia_emission_threshold
)
from lib.step_1_output_report.step_1_output_permit_report import (
    batch_generate_reports_permit,
    batch_or_single_upload_permit
)
from lib.step_1_output_report.step_2_output_executive_report import (
    batch_or_single_upload_anomaly,
    batch_generate_reports_anomaly
)


pd.set_option('future.no_silent_downcasting', True)

app = FastAPI()

# 设置允许访问的域名
origins = ["*"]  # "*"，即为所有。

# 设置跨域传参
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 设置允许的origins来源
    allow_credentials=True,
    allow_methods=["*"],  # 设置允许跨域的http方法，比如 get、post、put等。
    allow_headers=["*"])  # 允许跨域的headers，可以用来鉴别来源等作用


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """ # 添加中间件: 计算每次接口请求的响应时间; 接口请求异常捕获，并 """
    try:
        start_time = time.time()
        responser = await call_next(request)
        aut_token = request.headers.get('X-token', None)

        # base64.b64encode('rockontrol_air_quality_machine_learning'.encode('utf-8'))
        if request.method == 'OPTIONS':
            return Response(headers = {"Access-Control-Allow-Methods": "PUT,GET,POST,DELETE,OPTIONS",
                                       "Access-Control-Allow-Origin": "*",
                                       "Access-Control-Allow-Headers": "*",
                                       "Access-Control-Expose-Headers": "*",
                                       "Access-Control-Allow-Credentials": "true"},
                            content = "ok", status_code = 200)
        else:
            if aut_token != 'cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n':
                logger.info(f"user not authorized to route {request.url.path}")
                return Response(content = "no access", status_code = 401)

        process_time = round(time.time() - start_time, 4)
        # 返回接口响应时间
        responser.headers["X-Process-Time"] = f"{process_time} (s)"
        return responser

    except Exception as _:
        logger.error(f" error request: {request.url.path} \n {traceback.format_exc()}")
        raise HTTPException(status_code = 500, detail = {"message": f"{request.url.path} \
			接口请求异常，联系工程师修复."})


@app.get("/liveness")
async def check_liveness():
    return {"status": 'OK'}


def error_500():
    raise HTTPException(status_code=500, detail="遇到问题了，请联系相关开发人员。")


def validate_city_param(city: str) -> str:
    """
    校验城市参数的有效性

    Args:
        city: 城市名称

    Returns:
        str: 有效的城市名称

    Raises:
        HTTPException: 当城市无效时抛出400错误
    """
    if not city or not city.strip():
        raise HTTPException(
            status_code=400,
            detail="城市参数不能为空"
        )

    city = city.strip()

    if not city_manager.is_valid_city(city):
        supported_cities = city_manager.get_supported_cities()

        if not supported_cities:
            # 如果没有城市数据，说明数据库连接或数据有问题
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "城市数据服务不可用",
                    "message": "无法获取支持的城市列表，请联系管理员检查数据库连接",
                    "invalid_city": city
                }
            )

        error_detail = {
            "error": "城市参数无效",
            "invalid_city": city,
            "supported_cities": supported_cities[:20],  # 只返回前20个，避免响应过大
            "total_supported": len(supported_cities),
            "message": f"不支持的城市: '{city}'，请从支持的城市列表中选择"
        }
        if len(supported_cities) > 20:
            error_detail["note"] = "更多支持的城市请调用 GET /cities 接口查询"

        raise HTTPException(status_code=400, detail=error_detail)

    return city


@app.get('/cities')
async def get_supported_cities():
    """
    获取支持的城市列表

    Returns:
        dict: 包含城市列表和相关信息
            - status: "OK" 表示成功
            - data: 城市名称列表
            - count: 城市数量
            - cache_info: 缓存信息
    """
    try:
        cities = city_manager.get_supported_cities()
        cache_info = city_manager.get_cache_info()

        if not cities:
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "城市数据服务不可用",
                    "message": "无法获取支持的城市列表，请联系管理员检查数据库连接",
                    "cache_info": cache_info
                }
            )

        return {
            "status": "OK",
            "data": cities,
            "count": len(cities),
            "cache_info": cache_info
        }
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取城市列表失败，请联系管理员")



class CheckRequest(BaseModel):
    city: str = Body('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市")
    start_time: str = Body(..., description="校验的起始时间, e.g. 2021-01-01")
    end_time: str = Body(..., description="校验的结束时间, e.g. 2023-02-25")
    function: List[str] = Body(check_function, description=f'校验的功能, e.g. {check_function}')

    # 日期格式校验
    @field_validator("start_time", "end_time")
    def validate_date_format(cls, value):
        try:
            datetime.strptime(value, "%Y-%m-%d")
        except ValueError:
            raise ValueError("日期格式无效，应为 YYYY-MM-DD")
        return value

    # 城市有效性校验
    @field_validator("city")
    def validate_city(cls, value):
        if not city_manager.is_valid_city(value):
            supported_cities = city_manager.get_supported_cities()
            raise ValueError(f"城市无效，支持的城市: {supported_cities[:10]}{'...' if len(supported_cities) > 10 else ''}")
        return value


class ExeSelfMonitorRequest(BaseModel):
    city: str = Body('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市")
    years: List[int] = Body(None, description="年份列表，如[2023, 2024]，为空时查询所有年份")

    # 城市有效性校验
    @field_validator("city")
    def validate_city(cls, value):
        if not city_manager.is_valid_city(value):
            supported_cities = city_manager.get_supported_cities()
            raise ValueError(f"城市无效，支持的城市: {supported_cities[:10]}{'...' if len(supported_cities) > 10 else ''}")
        return value

    # 年份有效性校验
    @field_validator("years")
    def validate_years(cls, value):
        if value is not None:
            current_year = datetime.now().year
            for year in value:
                if year > current_year + 1:
                    raise ValueError(f"年份 {year} 超出有效范围 ({current_year + 1})")
        return value


class PermitEiaThresholdRequest(BaseModel):
    city: str = Body('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市")
    start_date: str = Body(..., description="起始时间，格式：YYYY-MM-DD, e.g. 2024-01-01")
    end_date: str = Body(..., description="结束时间，格式：YYYY-MM-DD, e.g. 2024-12-31")

    # 城市有效性校验
    @field_validator("city")
    def validate_city(cls, value):
        if not city_manager.is_valid_city(value):
            supported_cities = city_manager.get_supported_cities()
            raise ValueError(f"城市无效，支持的城市: {supported_cities[:10]}{'...' if len(supported_cities) > 10 else ''}")
        return value


@app.get('/permit-eia/oil-quantity')
def check_oil_quantity(
    city: str = Query('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市"),
):
    try:
        # 校验城市参数
        city = validate_city_param(city)
        logger.info(f"check_oil_quantity params: city `{city}`")
        res = run_oil_quantity(city)
        return {"status": "OK", "data": res}
    except HTTPException:
        raise  # 重新抛出HTTP异常
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/permit-eia/oil-volume')
def check_oil_volume(
    city: str = Query('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市"),
):
    try:
        # 校验城市参数
        city = validate_city_param(city)
        logger.info(f"check_oil_volume params: city `{city}`")
        res = run_oil_volume(city)
        return {"status": "OK", "data": res}
    except HTTPException:
        raise  # 重新抛出HTTP异常
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/permit-eia/port-base-info')
def check_port_base_info(
    city: str = Query('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市"),
):
    try:
        logger.info(f"check_port_base_info params: city `{city}`")
        res = run_port_base_info(city)
        return {"status": "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/permit-eia/gas-port')
def check_gas_port(
    city: str = Query('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市"),
):
    try:
        logger.info(f"check_gas_port params: city `{city}`")
        res = run_gas_port(city)
        return {"status": "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/all')
async def combined_report(
    city: str = Query('九龙坡区', description="校验的城市，支持的城市请调用 /cities 接口查询, e.g. 北京市"),
    start_time: str = Query(None, description="校验的起始时间, e.g. 2021-01-01 (用于 check 和 warning 接口)"),
    end_time: str = Query(None, description="校验的结束时间, e.g. 2023-02-25 (用于 check 和 warning 接口)"),
    time_str: str = Query('', description=f'校验的年份, e.g. 2021,2022,2023 (用于 year_emission 接口)'),
    rerun: int = Query(0, description="是否重新运行，默认为 0, 其他将重新执行"),
    task_id: str = Query(None, description="任务ID，用于查询已有计算结果"),
    ):

    # 首先校验城市参数
    city = validate_city_param(city)

    result = {
        'status': 200,
        'task_id': '',
        'date_range': [],
        'message': '',
        'data': {},
        'uploaded_files_info': []
    }
    try:
        logger.info(f"combined_report params: city `{city}` start_time `{start_time}`, end_time `{end_time}`, "
                    f"time `{time_str}`, rerun `{rerun}`, task_id `{task_id}`")
        task_id = task_id if task_id else f"city-{city}_start_time-{start_time}_end_time-{end_time}_time-{time_str}"

        if not rerun:
            if os.path.exists(f"{proj_dir}/data/jobs/{task_id}/result.json"):
                result = json.load(open(f"{proj_dir}/data/jobs/{task_id}/result.json"))
                return result
        else:
            try:
                shutil.rmtree(f"{proj_dir}/data/jobs/{task_id}", ignore_errors=True)
            except Exception as e:
                logger.info(f"No job directory found with task_id `{task_id}`")
            Global.clean()

        os.makedirs(f"{proj_dir}/data/jobs/{task_id}", exist_ok=True)
        time_ls = [int(i) for i in time_str.split(',')] if time_str else []

        if not (start_time and end_time and time):
            logger.info("No parameters provided, returning all reports.")
            raise HTTPException(status_code=400, detail="No parameters provided.")
  
        assert isinstance(rerun, int), "rerun must be an integer. 默认为 0, 其他将重新执行"

        result['date_range'] = [start_time, end_time]
        result['task_id'] = task_id
        
        # Execute check report
        json_anomoly = run(city, start_time, end_time, check_function)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_anomaly.json", 'w') as f:
            json.dump(json_anomoly, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_anomoly = json.load(f)
        
        # Execute warning report
        json_warning = run_warning(city, start_time, end_time)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_warning.json", 'w') as f:
            json.dump(json_warning, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_warning = json.load(f)

        # Execute year_emission report
        json_mismatch = run_year_emission(city, time_ls)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_year_emission.json", 'w') as f:
            json.dump(json_mismatch, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_mismatch = json.load(f)

        # Execute hospital_bed report
        json_bed = run_hosipital_bed(city)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_bed.json", 'w') as f:
            json.dump(json_bed, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_bed = json.load(f)

        json_oil_quantity = run_oil_quantity(city)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_oil_quantity.json", 'w') as f:
            json.dump(json_oil_quantity, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_oil_quantity = json.load(f)

        json_oil_volume = run_oil_volume(city)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_oil_volume.json", 'w') as f:
            json.dump(json_oil_volume, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_oil_volume = json.load(f)

        json_port_base_info = run_port_base_info(city)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_port_base_info.json", 'w') as f:
            json.dump(json_port_base_info, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_port_base_info = json.load(f)

        json_gas_port = run_gas_port(city)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_gas_port.json", 'w') as f:
            json.dump(json_gas_port, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_gas_port = json.load(f)

        json_year_emission_hp = run_year_emission_vs_hp(city, time_ls)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_year_emission_hp.json", 'w') as f:
            json.dump(json_year_emission_hp, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_year_emission_hp = json.load(f)

        json_exe_self_monitor = run_exe_with_self_monitor(city, time_ls)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_exe_self_monitor.json", 'w') as f:
            json.dump(json_exe_self_monitor, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_exe_self_monitor = json.load(f)

        # Execute exec_online_monitor_mismatch report
        json_exec_online_monitor = run_exec_online_monitor_mismatch(city, start_time, end_time)
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_exec_online_monitor_mismatch.json", 'w') as f:
            json.dump(json_exec_online_monitor, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_exec_online_monitor = json.load(f)

        # Execute exe_online_exceeding report
        json_exe_online_exceeding = run_exe_online_exceeding(city, start_time.split(' ')[0], end_time.split(' ')[0])
        with open(f"{proj_dir}/data/jobs/{result['task_id']}/result_exe_online_exceeding.json", 'w') as f:
            json.dump(json_exe_online_exceeding, f, indent=4, ensure_ascii=False, cls=MyEncoder)
            # json_exe_online_exceeding = json.load(f)

        upload_info_anomaly = batch_or_single_upload_anomaly(batch_generate_reports_anomaly(
            city,
            {
                'json_anomaly': json_anomoly,
                'json_mismatch': json_mismatch,
                'json_warning': json_warning,
                'json_exe_online_exceeding': json_exe_online_exceeding,
                'json_exec_online_monitor': json_exec_online_monitor,
                'json_exe_self_monitor': json_exe_self_monitor,
                'json_year_emission_hp': json_year_emission_hp
            },
            result['task_id'], rerun))
        upload_info_permit = batch_or_single_upload_permit(batch_generate_reports_permit(
            city,
            {
                'json_bed': json_bed,
                'json_oil_quantity': json_oil_quantity,
                'json_oil_volume': json_oil_volume,
                'json_port_base': json_port_base_info,
                'json_gas_port': json_gas_port,
             },
            result['task_id'], False, rerun), True)

        result['uploaded_files_info'] = {
            'executive_report_anomaly': upload_info_anomaly,
            'permit_anomaly': upload_info_permit
        }
        result['data'] = {
            'executive_report_anomaly': {
                **json_anomoly,
                'check_emission_mismatch': json_mismatch
            },
            'executive_report_warning': json_warning,
            'bed_mismatch': json_bed,
            'oil_quantity_mismatch': json_oil_quantity,
            'oil_volume_mismatch': json_oil_volume,
            'port_profile_mismatch': json_port_base_info,
            'gas_port_detail_mismatch': json_gas_port,
            'year_emission_eia_mismatch': json_year_emission_hp,
            'exe_self_monitor_anomalies': json_exe_self_monitor,
            'exec_online_monitor_mismatch': json_exec_online_monitor,
            'exe_online_exceeding_mismatch': json_exe_online_exceeding
        }
        result['status'] = 200
        
    except Exception as _:
        logger.error(traceback.format_exc())
        result['status'] = 400
        result['message'] = f"{traceback.format_exc()}"
    finally:
        if result['status'] == 200:
            os.makedirs(f'{proj_dir}/data/jobs/', exist_ok=True)
            with open(f"{proj_dir}/data/jobs/{result['task_id']}/result.json", 'w') as f:
                json.dump(result, f, indent=4, ensure_ascii=False, cls=MyEncoder)
        result = json.loads(json.dumps(result, cls=MyEncoder))
        return JSONResponse(result)

@app.post('/executive-report')
async def check(request: CheckRequest):
    try:
        logger.info(f"check params: city `{request.city}` start time `{request.start_time}` "
                    f"end time `{request.end_time}` function `{request.function}`")
        res = run(request.city, request.start_time, request.end_time, request.function)
        return {'status': 'OK', 'data': res}
    except Exception as _:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/executive-report/warning')
async def check_warning(
    city: str = Query('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
    start_time: str = Query(..., description='校验的起始时间, e.g. 2021-01-01'),
    end_time: str = Query(..., description='校验的结束时间, e.g. 2023-02-25'),
):
    try:
        logger.info(f"check_warning params: city `{city}` start time `{start_time}` end time `{end_time}`")
        res = run_warning(city, start_time, end_time)
        return {'status': "OK", "data": res}
    except Exception as _:
        logger.error(traceback.format_exc())
        error_500()


@app.post('/executive-report/licence/year')
async def check_year_emission_with_licence(
    city: str = Body('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
    time: List[int] = Body(..., description=f'校验的年份列表, e.g. [2021, 2022, 2023]'),
):
    try:
        logger.info(f"check_year_emission_with_licence params: city `{city}` time `{time}`")
        res = run_year_emission(city, time)
        return {'status': "OK", "data": res}
    except Exception as _:
        logger.error(traceback.format_exc())
        error_500()


@app.post('/executive-report/eia/year')
async def check_year_emission_vs_eia(
    city: str = Body('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
    years: List[int] = Body(..., description=f'校验的年份列表, e.g. [2021, 2022, 2023]'),
):
    """
    检查年度执行报告污染物排放量是否超过环评总量控制指标

    Args:
        city: 城市名称
        years: 检查年份列表（整数）

    Returns:
        检查结果，包含超标线索清单
    """
    try:
        logger.info(f"check_year_emission_vs_eia params: city `{city}` years `{years}`")

        res = run_year_emission_vs_hp(city, years)
        return {'status': "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.post('/executive-report/self-monitor')
async def check_exe_with_self_monitor_api(request: ExeSelfMonitorRequest):
    """
    执行报告与自行监测数据校验

    比较同一企业、同一排口、同一污染因子的执行报告与自行监测数据是否匹配。
    校验项目包括：有效监测数据数量、最大值、最小值、平均值。

    Args:
        request: 包含城市名称和年份列表的请求对象
            - city: 城市名称，支持的城市请调用 /cities 接口查询
            - years: 年份列表（可选），如[2023, 2024]，为空时查询所有年份

    Returns:
        dict: 校验结果
            - status: "OK" 表示成功
            - data: 异常记录列表，每条记录包含详细的异常信息

    Raises:
        HTTPException: 当参数验证失败或处理过程中发生错误时
    """
    try:
        logger.info(f"check_exe_with_self_monitor_api params: city `{request.city}` years `{request.years}`")

        start_time = time.time()
        res = run_exe_with_self_monitor(request.city, request.years)
        end_time = time.time()

        logger.info(f"check_exe_with_self_monitor_api completed in {end_time - start_time:.2f}s, "
                   f"found {len(res)} anomalies")

        return {'status': "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/executive-report/exec-online-monitor')
async def check_exec_online_monitor_mismatch_api(
    city: str = Query('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
    start_time: str = Query(..., description='校验的起始日期, e.g. 2021-01-01'),
    end_time: str = Query(..., description='校验的结束日期, e.g. 2023-02-25'),
):
    """
    季度执行报告记载的污染治理设施异常运转记录与在线监测数据工况标记不匹配检查

    Args:
        city: 城市名称
        start_time: 查询开始日期，格式为 'YYYY-MM-DD'
        end_time: 查询结束日期，格式为 'YYYY-MM-DD'

    Returns:
        dict: 校验结果
            - status: "OK" 表示成功
            - data: 不匹配记录列表，每条记录包含详细的不匹配信息
    """
    try:
        # 将日期转换为带时间的格式
        start_time = f"{start_time} 00:00:00"
        end_time = f"{end_time} 23:59:59"

        logger.info(f"check_exec_online_monitor_mismatch_api params: city `{city}` "
                    f"start_time `{start_time}` end_time `{end_time}`")

        start_time_exec = time.time()
        res = run_exec_online_monitor_mismatch(city, start_time, end_time)
        end_time_exec = time.time()

        logger.info(f"check_exec_online_monitor_mismatch_api completed in {end_time_exec - start_time_exec:.2f}s, "
                    f"found {len(res)} mismatch records")

        return {'status': "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/executive-report/online-exceeding')
async def check_exe_online_exceeding_api(
    city: str = Query('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
    start_date: str = Query(..., description='校验的起始日期, e.g. 2024-01-01'),
    end_date: str = Query(..., description='校验的结束日期, e.g. 2024-01-31'),
):
    """
    执行报告与在线监测超标数据校验

    校验在线监测企业废水废气的排口超标监测指标、超标时段、超标数据是否都在执行报告中有记录。

    功能特点：
    - 废气校验小时浓度，废水校验日均值浓度
    - 废气校验：氮氧化物、二氧化硫、非甲烷总烃、颗粒物（烟尘）
    - 废水校验：总磷、总氮、氨氮、化学需氧量
    - 只校验关联表中存在的企业排口污染物
    - 在线监测超标时间应在执行报告超标时间段内
    - 废水日均值在执行报告浓度的正负10%以内认为正确记录

    Args:
        city: 城市名称
        start_date: 查询开始日期，格式为 'YYYY-MM-DD'
        end_date: 查询结束日期，格式为 'YYYY-MM-DD'

    Returns:
        dict: 校验结果
            - status: "OK" 表示成功
            - data: 存在问题的记录数组，每个元素包含同一条在线监测记录的所有问题
    """
    try:
        logger.info(f"check_exe_online_exceeding_api params: city `{city}` "
                    f"start_date `{start_date}` end_date `{end_date}`")

        start_time_exec = time.time()
        res = run_exe_online_exceeding(city, start_date, end_date)
        end_time_exec = time.time()

        logger.info(f"check_exe_online_exceeding_api completed in {end_time_exec - start_time_exec:.2f}s, "
                    f"found {len(res)} problem records")

        return {'status': "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.post('/permit-eia/emission-threshold')
async def check_permit_eia_emission_threshold_api(request: PermitEiaThresholdRequest):
    """
    许可证记载的污染物全厂排放口总计或排口浓度或排口速率大于环评记载的排放限值检查

    检查许可证中记载的污染物排放数据是否超过环评中记载的排放限值，包括：
    1. 全厂排放口总计：比较许可证与环评的年排放量
    2. 排口浓度：比较许可证与环评的浓度限值
    3. 排口速率：比较许可证与环评的速率限值

    Args:
        request: 包含城市名称和时间范围的请求对象
            - city: 城市名称，支持的城市请调用 /cities 接口查询
            - start_date: 起始时间，格式：YYYY-MM-DD
            - end_date: 结束时间，格式：YYYY-MM-DD

    Returns:
        dict: 检查结果
            - status: "OK" 表示成功
            - data: 包含三种线索的字典
                - total_emission_clues: 全厂排放口总计线索列表
                - concentration_clues: 排口浓度线索列表
                - emission_rate_clues: 排口速率线索列表

    Raises:
        HTTPException: 当参数验证失败或处理过程中发生错误时
    """
    try:
        logger.info(f"check_permit_eia_emission_threshold_api params: city `{request.city}` "
                   f"start_date `{request.start_date}` end_date `{request.end_date}`")

        start_time = time.time()
        res = run_permit_eia_emission_threshold(request.city, request.start_date, request.end_date)
        end_time = time.time()

        total_clues = len(res.get('total_emission_clues', [])) + \
                     len(res.get('concentration_clues', [])) + \
                     len(res.get('emission_rate_clues', []))

        logger.info(f"check_permit_eia_emission_threshold_api completed in {end_time - start_time:.2f}s, "
                   f"found {total_clues} total clues "
                   f"(total_emission: {len(res.get('total_emission_clues', []))}, "
                   f"concentration: {len(res.get('concentration_clues', []))}, "
                   f"emission_rate: {len(res.get('emission_rate_clues', []))})")

        return {'status': "OK", "data": res}
    except Exception as e:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/lience/hospital-bed')
async def check_hosptial_bed(
    city: str = Query('九龙坡区', description='城市名称，支持的城市请调用 /cities 接口查询, e.g. "北京市"'),
):
    try:
        logger.info(f"check_hosptial_bed")
        res = run_hosipital_bed(city)
        return {'status': "OK", "data": res}
    except Exception as _:
        logger.error(traceback.format_exc())
        error_500()


@app.post('/llm/extract-graph')
async def llm_extract_graph(
    file1_type: str = Form(None, description='文件类型, ["许可证", "环评", "执行报告"]'),
    file2_type: str = Form(None, description='文件类型, ["许可证", "环评", "执行报告"]'),
    task_id: str = Form('', description='任务ID'),
    file1: UploadFile = File(None, description='文件1'),
    file2: UploadFile = File(None, description='文件2')
):
    task_manager = TaskManager(max_task=1)
    if task_id:
        logger.info(f"llm_extract_graph params: task_id `{task_id}`")
        got, info = task_manager.get_task_result(task_id)
        if got:
            info['status'] = 'OK'
            return info
        else:
            return {
                "status": "Fail",
                "data": "",
                "message": info
            }

    if file1 is None or file2 is None or file1_type is None or file2_type is None:
        return JSONResponse(
            content={"error": "Both files and file types must be provided."},
            status_code=400,
        )

    if not file1.filename.endswith(".docx") or not file2.filename.endswith(".docx"):
        return JSONResponse(
            content={"error": "Both files must be .docx format."},
            status_code=400,
        )
    if file1_type == file2_type:
        return JSONResponse(
            content={"error": "Both files must be different types."},
            status_code=400,
        )

    # 文件类型必须是"许可证", "环评", "执行报告"之一，并且必须有一个为"许可证"
    if file1_type not in ["许可证", "环评", "执行报告"] or file2_type not in ["许可证", "环评", "执行报告"]:
        return JSONResponse(
            content={"error": '文件类型必须是"许可证", "环评", "执行报告"'},
            status_code=400,
        )
    if file1_type != "许可证" and file2_type != "许可证":
        return JSONResponse(
            content={"error": "缺少许可证文件"},
            status_code=400,
        )

    try:
        file1_name, file2_name = file1.filename, file2.filename
        logger.info(
            f"llm_extract_graph params: file1_type `{file1_type}` file1_name `{file1_name}` "
            f"file2_type `{file2_type}` file2_name `{file2_name}`"
        )

        file1_data = await file1.read()
        file2_data = await file2.read()
        md5_i = calc_web_data_md5(file1_data)
        md5_j = calc_web_data_md5(file2_data)
        md5 = calc_str_md5(''.join(sorted([md5_i, md5_j])))

        got, info = task_manager.get_task_result(md5)
        if got:
            info['status'] = 'OK'
            return info

        try:
            task_manager.add_task(md5)
        except Exception as e:
            task_manager.done(md5)
            return {'status': "OK", "data": "", "id": md5, "message": str(e)}
        else:
            worker = threading.Thread(
                target = thread_run_extract_graph,
                args = (file1_type, file2_type, file1_name, file2_name, file1_data, file2_data, task_manager, md5)
            )
            worker.start()

        return {'status': "OK", "data": "", "id": md5, "message": "任务已提交"}
    except:
        logger.error(traceback.format_exc())
        error_500()


@app.get('/cache/enterprise/stats')
async def get_enterprise_cache_stats():
    """
    获取企业数据缓存统计信息

    Returns:
        dict: 缓存统计信息，包括命中率、缓存大小等
    """
    try:
        from lib.check.base import get_cache_stats
        stats = get_cache_stats()
        return {"status": "OK", "data": stats}
    except Exception as e:
        logger.error(f"获取缓存统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取缓存统计信息失败")


@app.delete('/cache/enterprise')
async def clear_enterprise_cache():
    """
    清空企业数据缓存

    Returns:
        dict: 操作结果
    """
    try:
        from lib.check.base import clear_enterprise_cache
        clear_enterprise_cache()
        logger.info("企业数据缓存已通过API清空")
        return {"status": "OK", "message": "企业数据缓存已清空"}
    except Exception as e:
        logger.error(f"清空企业缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail="清空企业缓存失败")


@app.delete('/cache/{Type}')
async def rm_cache(
    Type: str = Path(
        ...,
        regex="^(jobs|graph_jobs|enterprise)$",
        example="jobs",
        description="允许的值：'jobs'、'graph_jobs' 或 'enterprise'"
    ),
    Id: str = Query('all', description="缓存数据id")
):
    logger.info(f"rm_cache params: Type `{Type}` Id `{Id}`")
    try:
        if Type == 'jobs':
            Global.clean()
        elif Type == 'enterprise':
            from lib.check.base import clear_enterprise_cache
            clear_enterprise_cache()
        else:
            remove_files(Type, Id)
        return {"status": "OK"}
    except FileExistsError:
        raise HTTPException(status_code=404, detail=f"没有找到缓存'{Id}'")


if __name__ == "__main__":
    create_lock_file()
    uvicorn.run(app = 'web_server:app', port = 8080, host = "0.0.0.0", workers = 1, log_config=_log_conf)
